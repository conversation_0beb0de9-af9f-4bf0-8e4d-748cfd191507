<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ACME Inc. - Road Runner Equipment Specialists</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Comic Sans MS", cursive, Arial, sans-serif;
        background: linear-gradient(
          135deg,
          #ff6b35 0%,
          #f7931e 50%,
          #ffe066 100%
        );
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
      }

      /* Animated background elements */
      .cloud {
        position: absolute;
        background: white;
        border-radius: 50px;
        opacity: 0.8;
        animation: float 20s infinite linear;
      }

      .cloud:before {
        content: "";
        position: absolute;
        background: white;
        border-radius: 50px;
      }

      .cloud1 {
        width: 80px;
        height: 30px;
        top: 20%;
        animation-duration: 25s;
      }

      .cloud1:before {
        width: 50px;
        height: 40px;
        top: -20px;
        left: 10px;
      }

      .cloud2 {
        width: 60px;
        height: 25px;
        top: 60%;
        animation-duration: 30s;
      }

      .cloud2:before {
        width: 40px;
        height: 30px;
        top: -15px;
        left: 15px;
      }

      @keyframes float {
        from {
          transform: translateX(-100px);
        }
        to {
          transform: translateX(calc(100vw + 100px));
        }
      }

      .login-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        padding: 40px;
        width: 450px;
        text-align: center;
        border: 5px solid #8b4513;
        position: relative;
        z-index: 10;
      }

      .acme-logo {
        background: linear-gradient(45deg, #ff6b35, #8b4513);
        color: white;
        padding: 15px 30px;
        border-radius: 15px;
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 20px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        border: 3px solid #654321;
      }

      .character-placeholder {
        background: linear-gradient(135deg, #87ceeb, #4169e1);
        border: 3px solid #8b4513;
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
        color: white;
        font-size: 14px;
        font-weight: bold;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
      }

      .login-form {
        margin: 30px 0;
      }

      .form-group {
        margin-bottom: 20px;
        text-align: left;
      }

      .form-label {
        display: block;
        margin-bottom: 5px;
        color: #8b4513;
        font-weight: bold;
        font-size: 14px;
      }

      .form-input {
        width: 100%;
        padding: 12px 15px;
        border: 3px solid #ff6b35;
        border-radius: 10px;
        font-size: 16px;
        background: #fff8dc;
        transition: all 0.3s ease;
      }

      .form-input:focus {
        outline: none;
        border-color: #8b4513;
        box-shadow: 0 0 10px rgba(255, 107, 53, 0.5);
        transform: scale(1.02);
      }

      .login-btn {
        background: linear-gradient(45deg, #ff6b35, #ff4500);
        color: white;
        border: none;
        padding: 15px 40px;
        font-size: 18px;
        font-weight: bold;
        border-radius: 25px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-transform: uppercase;
        border: 3px solid #8b4513;
        margin: 20px 0;
      }

      .login-btn:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 10px 20px rgba(255, 107, 53, 0.4);
        background: linear-gradient(45deg, #ff4500, #ff6b35);
      }

      .login-btn:active {
        transform: translateY(-1px) scale(1.02);
      }

      .footer-links {
        margin-top: 20px;
        font-size: 12px;
      }

      .footer-links a {
        color: #8b4513;
        text-decoration: none;
        margin: 0 10px;
        font-weight: bold;
      }

      .footer-links a:hover {
        text-decoration: underline;
        color: #ff6b35;
      }

      .tagline {
        background: linear-gradient(45deg, #8b4513, #cd853f);
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        margin-top: 20px;
        font-style: italic;
        font-weight: bold;
        border: 2px solid #654321;
      }

      /* Fun animation for the login button */
      @keyframes bounce {
        0%,
        20%,
        50%,
        80%,
        100% {
          transform: translateY(0);
        }
        40% {
          transform: translateY(-10px);
        }
        60% {
          transform: translateY(-5px);
        }
      }

      .login-btn:hover {
        animation: bounce 0.6s ease-in-out;
      }

      /* Dust cloud animation */
      .dust-cloud {
        position: absolute;
        width: 50px;
        height: 30px;
        background: rgba(139, 69, 19, 0.3);
        border-radius: 50%;
        bottom: 10%;
        right: -60px;
        animation: dust 15s infinite linear;
      }

      @keyframes dust {
        from {
          transform: translateX(100vw) scale(0.5);
          opacity: 0.8;
        }
        to {
          transform: translateX(-100px) scale(1.2);
          opacity: 0.2;
        }
      }
    </style>
  </head>
  <body>
    <!-- Animated background elements -->
    <div class="cloud cloud1"></div>
    <div class="cloud cloud2"></div>
    <div class="dust-cloud"></div>

    <div class="login-container">
      <div class="acme-logo">
        <img
          src="images/Acme-corp.webp"
          alt="ACME INC."
          style="height: 60px; width: auto"
        />
      </div>

      <div class="character-placeholder">
        [INSERT: Wile E. Coyote looking confident with blueprints]<br />
        Super Genius at Work! 🧠⚡
      </div>

      <form class="login-form" onsubmit="handleLogin(event)">
        <div class="form-group">
          <label class="form-label">🏃‍♂️ Employee ID</label>
          <input
            type="text"
            class="form-input"
            placeholder="Enter your ACME ID..."
            required
          />
        </div>

        <div class="form-group">
          <label class="form-label">🔐 Password</label>
          <input
            type="password"
            class="form-input"
            placeholder="Top Secret Code..."
            required
          />
        </div>

        <button type="submit" class="login-btn">🚀 Begin Hunt!</button>
      </form>

      <div class="footer-links">
        <a href="#forgot">💭 Forgot Password?</a>
        <span>|</span>
        <a href="#help">❓ Need Help?</a>
      </div>

      <div class="tagline">"We Put the 'BOOM' in Business!" 💥</div>
    </div>

    <script>
      function handleLogin(event) {
        event.preventDefault();

        // Fun loading animation
        const btn = event.target.querySelector(".login-btn");
        const originalText = btn.innerHTML;

        btn.innerHTML = "🌪️ Activating...";
        btn.disabled = true;

        setTimeout(() => {
          // Simulate redirect to dashboard
          alert("🎯 Welcome to ACME Inc.! Redirecting to your dashboard...");
          btn.innerHTML = originalText;
          btn.disabled = false;
        }, 2000);
      }

      // Add some interactive elements
      document.addEventListener("DOMContentLoaded", function () {
        const inputs = document.querySelectorAll(".form-input");
        inputs.forEach((input) => {
          input.addEventListener("focus", function () {
            this.style.background = "#FFFFE0";
          });
          input.addEventListener("blur", function () {
            this.style.background = "#FFF8DC";
          });
        });
      });
    </script>
  </body>
</html>
