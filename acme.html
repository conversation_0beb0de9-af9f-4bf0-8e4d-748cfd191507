<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>ACME Co. - Inventory Dashboard</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Comic Sans MS", cursive, Arial, sans-serif;
        background: linear-gradient(180deg, #87ceeb 0%, #f0e68c 100%);
        min-height: 100vh;
      }

      /* Header/Navigation */
      .header {
        background: linear-gradient(45deg, #8b4513, #cd853f);
        padding: 15px 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        border-bottom: 5px solid #654321;
      }

      .logo-section {
        display: flex;
        align-items: center;
        color: white;
      }

      .logo {
        font-size: 24px;
        font-weight: bold;
        margin-right: 20px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
      }

      .nav-menu {
        display: flex;
        list-style: none;
        margin: 0;
      }

      .nav-item {
        margin: 0 15px;
      }

      .nav-link {
        color: white;
        text-decoration: none;
        font-weight: bold;
        padding: 8px 15px;
        border-radius: 20px;
        transition: all 0.3s ease;
        border: 2px solid transparent;
      }

      .nav-link:hover,
      .nav-link.active {
        background: rgba(255, 255, 255, 0.2);
        border-color: #ff6b35;
        transform: translateY(-2px);
      }

      .user-profile {
        display: flex;
        align-items: center;
        color: white;
      }

      .user-avatar {
        width: 40px;
        height: 40px;
        background: #ff6b35;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        margin-right: 10px;
        border: 3px solid white;
      }

      /* Main Layout */
      .main-layout {
        display: flex;
        min-height: calc(100vh - 80px);
      }

      .sidebar {
        width: 250px;
        background: linear-gradient(180deg, #deb887 0%, #d2b48c 100%);
        padding: 20px 0;
        box-shadow: 4px 0 8px rgba(0, 0, 0, 0.2);
        border-right: 5px solid #8b4513;
      }

      .sidebar-menu {
        list-style: none;
      }

      .sidebar-item {
        margin: 5px 15px;
      }

      .sidebar-link {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        color: #8b4513;
        text-decoration: none;
        font-weight: bold;
        border-radius: 10px;
        transition: all 0.3s ease;
        border: 2px solid transparent;
      }

      .sidebar-link:hover,
      .sidebar-link.active {
        background: rgba(255, 107, 53, 0.2);
        border-color: #ff6b35;
        transform: translateX(5px);
      }

      .sidebar-icon {
        margin-right: 10px;
        font-size: 18px;
      }

      /* Main Content */
      .content {
        flex: 1;
        padding: 30px;
      }

      .welcome-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        margin-bottom: 30px;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        border: 3px solid #ff6b35;
      }

      .welcome-title {
        color: #8b4513;
        font-size: 24px;
        margin-bottom: 10px;
      }

      .character-quote {
        background: linear-gradient(45deg, #ffe066, #f7931e);
        padding: 15px 20px;
        border-radius: 20px;
        border: 2px solid #8b4513;
        font-style: italic;
        color: #654321;
        font-weight: bold;
        text-align: center;
      }

      /* Stats Cards */
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
      }

      .stat-card {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        border: 3px solid #ff6b35;
        text-align: center;
        transition: transform 0.3s ease;
      }

      .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 24px rgba(0, 0, 0, 0.2);
      }

      .stat-icon {
        font-size: 48px;
        margin-bottom: 10px;
      }

      .stat-number {
        font-size: 32px;
        font-weight: bold;
        color: #8b4513;
        margin-bottom: 5px;
      }

      .stat-label {
        color: #666;
        font-weight: bold;
      }

      /* Inventory Table */
      .inventory-section {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
        border: 3px solid #ff6b35;
        margin-bottom: 30px;
      }

      .section-title {
        color: #8b4513;
        font-size: 20px;
        margin-bottom: 20px;
        display: flex;
        align-items: center;
      }

      .section-title::before {
        content: "📦";
        margin-right: 10px;
      }

      .inventory-table {
        width: 100%;
        border-collapse: collapse;
        border-radius: 10px;
        overflow: hidden;
      }

      .inventory-table th {
        background: linear-gradient(45deg, #8b4513, #cd853f);
        color: white;
        padding: 15px;
        text-align: left;
        font-weight: bold;
      }

      .inventory-table td {
        padding: 15px;
        border-bottom: 2px solid #f0e68c;
      }

      .inventory-table tr:nth-child(even) {
        background: #fff8dc;
      }

      .inventory-table tr:hover {
        background: #ffe4b5;
        transform: scale(1.01);
      }

      .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-weight: bold;
        font-size: 12px;
      }

      .status-in-stock {
        background: #98fb98;
        color: #006400;
      }

      .status-low-stock {
        background: #ffe4b5;
        color: #ff8c00;
      }

      .status-out-of-stock {
        background: #ffb6c1;
        color: #dc143c;
      }

      /* Action Buttons */
      .action-buttons {
        display: flex;
        gap: 15px;
        margin-top: 20px;
        justify-content: center;
      }

      .action-btn {
        padding: 12px 25px;
        border: none;
        border-radius: 25px;
        font-weight: bold;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 3px solid transparent;
        font-size: 14px;
      }

      .btn-primary {
        background: linear-gradient(45deg, #ff6b35, #ff4500);
        color: white;
        border-color: #8b4513;
      }

      .btn-secondary {
        background: linear-gradient(45deg, #deb887, #cd853f);
        color: white;
        border-color: #8b4513;
      }

      .action-btn:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
      }

      /* Fun animations */
      @keyframes roadrunner {
        0% {
          transform: translateX(-50px);
        }
        50% {
          transform: translateX(20px) scaleX(-1);
        }
        100% {
          transform: translateX(-50px);
        }
      }

      .roadrunner-animation {
        position: fixed;
        bottom: 20px;
        right: 20px;
        animation: roadrunner 8s infinite ease-in-out;
        z-index: 1000;
      }

      /* Character placeholders */
      .character-placeholder {
        background: linear-gradient(135deg, #87ceeb, #4169e1);
        border: 3px solid #8b4513;
        border-radius: 15px;
        padding: 15px;
        color: white;
        font-size: 12px;
        font-weight: bold;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
        text-align: center;
        margin: 10px 0;
      }
    </style>
  </head>
  <body>
    <!-- Header -->
    <header class="header">
      <div class="logo-section">
        <div class="logo">🏭 ACME Co.</div>
        <nav>
          <ul class="nav-menu">
            <li class="nav-item">
              <a href="#" class="nav-link active">Dashboard</a>
            </li>
            <li class="nav-item"><a href="#" class="nav-link">Inventory</a></li>
            <li class="nav-item"><a href="#" class="nav-link">Orders</a></li>
            <li class="nav-item"><a href="#" class="nav-link">Reports</a></li>
          </ul>
        </nav>
      </div>

      <div class="user-profile">
        <div class="user-avatar">🐺</div>
        <div>
          <div style="font-size: 14px">Wile E. Coyote</div>
          <div style="font-size: 12px; opacity: 0.8">Super Genius</div>
        </div>
      </div>
    </header>

    <!-- Main Layout -->
    <div class="main-layout">
      <!-- Sidebar -->
      <aside class="sidebar">
        <ul class="sidebar-menu">
          <li class="sidebar-item">
            <a href="#" class="sidebar-link active">
              <span class="sidebar-icon">📊</span>
              Dashboard
            </a>
          </li>
          <li class="sidebar-item">
            <a href="#" class="sidebar-link">
              <span class="sidebar-icon">📦</span>
              Inventory
            </a>
          </li>
          <li class="sidebar-item">
            <a href="#" class="sidebar-link">
              <span class="sidebar-icon">🛒</span>
              Orders
            </a>
          </li>
          <li class="sidebar-item">
            <a href="#" class="sidebar-link">
              <span class="sidebar-icon">🎯</span>
              Traps & Gadgets
            </a>
          </li>
          <li class="sidebar-item">
            <a href="#" class="sidebar-link">
              <span class="sidebar-icon">💥</span>
              Explosives
            </a>
          </li>
          <li class="sidebar-item">
            <a href="#" class="sidebar-link">
              <span class="sidebar-icon">📈</span>
              Reports
            </a>
          </li>
          <li class="sidebar-item">
            <a href="#" class="sidebar-link">
              <span class="sidebar-icon">⚙️</span>
              Settings
            </a>
          </li>
        </ul>

        <div class="character-placeholder" style="margin: 20px 15px">
          [INSERT: Road Runner saying "Beep Beep!"]<br />
          💨 Speed is Key! 💨
        </div>
      </aside>

      <!-- Main Content -->
      <main class="content">
        <!-- Welcome Section -->
        <div class="welcome-section">
          <h1 class="welcome-title">Welcome back, Super Genius! 🧠</h1>
          <div class="character-quote">
            <div class="character-placeholder">
              [INSERT: Wile E. Coyote with clipboard saying:]<br />
              "Today's mission: Perfect inventory management for the perfect
              trap!"
            </div>
          </div>
        </div>

        <!-- Stats Grid -->
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-icon">📦</div>
            <div class="stat-number">1,247</div>
            <div class="stat-label">Total Items</div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">⚠️</div>
            <div class="stat-number">23</div>
            <div class="stat-label">Low Stock Alert</div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">🚚</div>
            <div class="stat-number">87</div>
            <div class="stat-label">Pending Orders</div>
          </div>

          <div class="stat-card">
            <div class="stat-icon">💥</div>
            <div class="stat-number">156</div>
            <div class="stat-label">Explosive Items</div>
          </div>
        </div>

        <!-- Inventory Section -->
        <div class="inventory-section">
          <h2 class="section-title">Recent Inventory Updates</h2>

          <table class="inventory-table">
            <thead>
              <tr>
                <th>Item Code</th>
                <th>Product Name</th>
                <th>Category</th>
                <th>Stock Level</th>
                <th>Status</th>
                <th>Last Updated</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td><strong>ACM-001</strong></td>
                <td>Super Magnet 3000</td>
                <td>🧲 Magnets</td>
                <td>45 units</td>
                <td>
                  <span class="status-badge status-in-stock">In Stock</span>
                </td>
                <td>2 hours ago</td>
              </tr>
              <tr>
                <td><strong>ACM-002</strong></td>
                <td>Rocket Propelled Skates</td>
                <td>🚀 Transportation</td>
                <td>12 units</td>
                <td>
                  <span class="status-badge status-low-stock">Low Stock</span>
                </td>
                <td>5 hours ago</td>
              </tr>
              <tr>
                <td><strong>ACM-003</strong></td>
                <td>Earthquake Pills</td>
                <td>💊 Consumables</td>
                <td>0 units</td>
                <td>
                  <span class="status-badge status-out-of-stock"
                    >Out of Stock</span
                  >
                </td>
                <td>1 day ago</td>
              </tr>
              <tr>
                <td><strong>ACM-004</strong></td>
                <td>Portable Hole Kit</td>
                <td>🕳️ Utilities</td>
                <td>78 units</td>
                <td>
                  <span class="status-badge status-in-stock">In Stock</span>
                </td>
                <td>3 hours ago</td>
              </tr>
              <tr>
                <td><strong>ACM-005</strong></td>
                <td>Instant Bolder Dropper</td>
                <td>🪨 Traps</td>
                <td>5 units</td>
                <td>
                  <span class="status-badge status-low-stock">Low Stock</span>
                </td>
                <td>6 hours ago</td>
              </tr>
              <tr>
                <td><strong>ACM-006</strong></td>
                <td>Jet-Powered Pogo Stick</td>
                <td>🚀 Transportation</td>
                <td>34 units</td>
                <td>
                  <span class="status-badge status-in-stock">In Stock</span>
                </td>
                <td>4 hours ago</td>
              </tr>
            </tbody>
          </table>

          <div class="action-buttons">
            <button
              class="action-btn btn-primary"
              onclick="showAlert('Adding new item to inventory!')"
            >
              ➕ Add New Item
            </button>
            <button
              class="action-btn btn-secondary"
              onclick="showAlert('Generating inventory report!')"
            >
              📊 Generate Report
            </button>
            <button
              class="action-btn btn-secondary"
              onclick="showAlert('Exporting data to CSV!')"
            >
              💾 Export Data
            </button>
            <button
              class="action-btn btn-primary"
              onclick="showAlert('Restocking low inventory items!')"
            >
              🔄 Restock Items
            </button>
          </div>
        </div>

        <!-- Quick Actions Section -->
        <div class="inventory-section">
          <h2 class="section-title">🎯 Quick Hunt Actions</h2>

          <div class="character-placeholder">
            [INSERT: Wile E. Coyote with various ACME products around him]<br />
            "Choose your weapons wisely, fellow genius!"
          </div>

          <div class="stats-grid">
            <div
              class="stat-card"
              style="cursor: pointer"
              onclick="showAlert('Opening Giant Slingshot configurator!')"
            >
              <div class="stat-icon">🏹</div>
              <div style="color: #8b4513; font-weight: bold">
                Giant Slingshot
              </div>
              <div class="stat-label">Configure Now</div>
            </div>

            <div
              class="stat-card"
              style="cursor: pointer"
              onclick="showAlert('Activating Tunnel Boring Machine!')"
            >
              <div class="stat-icon">🕳️</div>
              <div style="color: #8b4513; font-weight: bold">Tunnel System</div>
              <div class="stat-label">Deploy Tunnels</div>
            </div>

            <div
              class="stat-card"
              style="cursor: pointer"
              onclick="showAlert('Preparing Dynamite Bundle!')"
            >
              <div class="stat-icon">🧨</div>
              <div style="color: #8b4513; font-weight: bold">
                Explosives Kit
              </div>
              <div class="stat-label">Arm & Deploy</div>
            </div>

            <div
              class="stat-card"
              style="cursor: pointer"
              onclick="showAlert('Launching Weather Control Device!')"
            >
              <div class="stat-icon">🌪️</div>
              <div style="color: #8b4513; font-weight: bold">
                Weather Machine
              </div>
              <div class="stat-label">Control Climate</div>
            </div>
          </div>
        </div>
      </main>
    </div>

    <script>
      function showAlert(message) {
        alert(message);
      }

      // Add some interactive hover effects
      document.addEventListener("DOMContentLoaded", function () {
        const statCards = document.querySelectorAll(".stat-card");
        statCards.forEach((card) => {
          card.addEventListener("mouseenter", function () {
            this.style.transform = "translateY(-8px) rotate(1deg)";
          });
          card.addEventListener("mouseleave", function () {
            this.style.transform = "translateY(0) rotate(0deg)";
          });
        });

        // Add clicking sound effect simulation
        const actionButtons = document.querySelectorAll(".action-btn");
        actionButtons.forEach((btn) => {
          btn.addEventListener("click", function () {
            this.style.transform = "scale(0.95)";
            setTimeout(() => {
              this.style.transform = "";
            }, 150);
          });
        });
      });

      // Simulate live updates
      setInterval(function () {
        const timeElements = document.querySelectorAll("td:last-child");
        const randomIndex = Math.floor(
          Math.random() * (timeElements.length - 1)
        );
        if (
          timeElements[randomIndex] &&
          timeElements[randomIndex].textContent.includes("ago")
        ) {
          timeElements[randomIndex].style.background = "#98FB98";
          setTimeout(() => {
            timeElements[randomIndex].style.background = "";
          }, 2000);
        }
      }, 30000);
    </script>
  </body>
</html>
